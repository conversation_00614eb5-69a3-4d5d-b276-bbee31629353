[{"id": 1, "title": "Brain Tumor Detection AI", "description": "Advanced medical AI system using deep learning to detect and classify brain tumors from MRI scans with 94.5% accuracy. Features automated preprocessing, real-time inference, and detailed diagnostic reports.", "image": "/images/projects/brain-tumor-ai.jpg", "technologies": ["TensorFlow", "OpenCV", "Flask", "<PERSON>er", "Python", "CNN"], "features": ["94.5% accuracy in tumor detection", "Real-time MRI scan processing", "Automated diagnostic reports", "Docker containerized deployment"], "github": "https://github.com/huckbyte/braintumor", "demo": null, "status": "completed", "category": "AI/ML"}, {"id": 2, "title": "Forex Hub Platform", "description": "Real-time forex trading dashboard processing over $50K in monthly transactions. Features live market data, trading signals, portfolio management, and advanced analytics with WebSocket integration.", "image": "/images/projects/forex-hub.jpg", "technologies": ["React.js", "Node.js", "Express.js", "MongoDB", "WebSockets", "Chart.js"], "features": ["Real-time market data streaming", "Advanced trading analytics", "Portfolio management system", "Processing $50K+ monthly transactions"], "github": null, "demo": "https://forexhub.quatromgt.co.ke/", "status": "live", "category": "Full Stack"}, {"id": 3, "title": "Multilingual Speech Recognition App", "description": "Advanced speech-to-text application supporting multiple languages with real-time transcription. Built using Web Speech API with cloud integration for enhanced accuracy and performance.", "image": "/images/projects/speech-recognition.jpg", "technologies": ["Web Speech API", "React.js", "Google Cloud Platform", "JavaScript", "CSS3"], "features": ["Multi-language support", "Real-time speech transcription", "Cloud-based processing", "Responsive web interface"], "github": "https://github.com/huckbyte/face-Recognition-Project", "demo": null, "status": "completed", "category": "AI/ML"}, {"id": 4, "title": "E-Commerce Analytics Dashboard", "description": "Comprehensive analytics platform for e-commerce businesses with real-time sales tracking, customer insights, and predictive analytics. Features advanced data visualization and automated reporting.", "image": "/images/projects/ecommerce-analytics.jpg", "technologies": ["React.js", "D3.js", "Node.js", "PostgreSQL", "Redis", "<PERSON>er"], "features": ["Real-time sales analytics", "Customer behavior tracking", "Predictive sales forecasting", "Automated report generation"], "github": "https://github.com/huckbyte/ecommerce-analytics", "demo": null, "status": "completed", "category": "Full Stack"}, {"id": 5, "title": "Cybersecurity Threat Detection System", "description": "AI-powered cybersecurity system that detects and prevents network intrusions in real-time. Uses machine learning algorithms to identify suspicious patterns and automatically respond to threats.", "image": "/images/projects/cybersecurity-system.jpg", "technologies": ["Python", "Scikit-learn", "TensorFlow", "Wireshark", "Flask", "MongoDB"], "features": ["Real-time threat detection", "Automated incident response", "Network traffic analysis", "Machine learning-based pattern recognition"], "github": "https://github.com/huckbyte/cyber-threat-detection", "demo": null, "status": "completed", "category": "Cybersecurity"}, {"id": 6, "title": "Smart IoT Home Automation", "description": "Intelligent home automation system with voice control, mobile app integration, and energy optimization. Features smart scheduling, security monitoring, and environmental controls.", "image": "/images/projects/iot-home.jpg", "technologies": ["<PERSON><PERSON><PERSON><PERSON>", "Raspberry Pi", "React Native", "Firebase", "Python", "MQTT"], "features": ["Voice-controlled devices", "Mobile app integration", "Energy usage optimization", "Security monitoring system"], "github": "https://github.com/huckbyte/smart-home-iot", "demo": null, "status": "in-progress", "category": "IoT"}]