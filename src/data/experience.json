[{"id": 1, "title": "Remote Software Engineer", "company": "Freelance", "location": "Remote", "period": "2022 – Present", "type": "Full-time", "description": "Building and deploying production-level web applications for clients worldwide", "achievements": ["Built 15+ apps with React/Node/MongoDB, integrated APIs, handled 10K+ users", "Improved app performance by 40% through optimization techniques", "Maintained 99.9% uptime across all deployed applications", "Implemented real-time features using WebSockets and modern frameworks"], "technologies": ["React", "Node.js", "MongoDB", "Express.js", "JavaScript", "TypeScript"]}, {"id": 2, "title": "AI Research Intern", "company": "Intelinova Institute Lab", "location": "Nairobi, Kenya", "period": "2022 – Present", "type": "Internship", "description": "Conducting cutting-edge research in artificial intelligence and machine learning", "achievements": ["Built CNNs for medical AI achieving 94.5% accuracy in brain tumor detection", "Automated data pipelines for processing medical imaging datasets", "Published findings in 2 research papers on computer vision applications", "Developed ML models using TensorFlow and PyTorch for healthcare applications"], "technologies": ["TensorFlow", "PyTorch", "OpenCV", "Python", "Scikit-learn", "<PERSON>er"]}]