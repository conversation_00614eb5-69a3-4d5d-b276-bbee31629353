import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'

const CircularProgress = ({ percentage, size = 120, strokeWidth = 8, delay = 0 }) => {
  const [animatedPercentage, setAnimatedPercentage] = useState(0)
  
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (animatedPercentage / 100) * circumference

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedPercentage(percentage)
    }, delay)
    
    return () => clearTimeout(timer)
  }, [percentage, delay])

  const getColor = (percent) => {
    if (percent >= 90) return '#22c55e' // Green for Expert
    if (percent >= 80) return '#3b82f6' // Blue for Advanced  
    if (percent >= 70) return '#f59e0b' // Orange for Intermediate
    return '#ef4444' // Red for Beginner
  }

  const getGradientId = (percent) => {
    if (percent >= 90) return 'expert-gradient'
    if (percent >= 80) return 'advanced-gradient'
    if (percent >= 70) return 'intermediate-gradient'
    return 'beginner-gradient'
  }

  return (
    <div className="circular-progress" style={{ width: size, height: size }}>
      <svg width={size} height={size} className="circular-progress-svg">
        <defs>
          <linearGradient id="expert-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#22c55e" />
            <stop offset="100%" stopColor="#16a34a" />
          </linearGradient>
          <linearGradient id="advanced-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#3b82f6" />
            <stop offset="100%" stopColor="#2563eb" />
          </linearGradient>
          <linearGradient id="intermediate-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#f59e0b" />
            <stop offset="100%" stopColor="#d97706" />
          </linearGradient>
          <linearGradient id="beginner-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#ef4444" />
            <stop offset="100%" stopColor="#dc2626" />
          </linearGradient>
        </defs>
        
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke="rgba(255, 255, 255, 0.1)"
          strokeWidth={strokeWidth}
        />
        
        {/* Progress circle */}
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke={`url(#${getGradientId(percentage)})`}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
          initial={{ strokeDashoffset: circumference }}
          animate={{ strokeDashoffset }}
          transition={{ 
            duration: 2, 
            delay: delay / 1000,
            ease: "easeOut" 
          }}
        />
      </svg>
      
      {/* Percentage text */}
      <div className="circular-progress-text">
        <motion.span 
          className="percentage"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: delay / 1000 + 0.5 }}
        >
          {animatedPercentage}%
        </motion.span>
      </div>
    </div>
  )
}

export default CircularProgress
