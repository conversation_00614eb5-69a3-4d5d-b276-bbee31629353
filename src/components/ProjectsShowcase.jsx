import { motion, AnimatePresence } from 'framer-motion'
import { useState } from 'react'
import { Github, ExternalLink, X, Code, Award, Calendar, MapPin } from 'lucide-react'

const ProjectsShowcase = ({ projectsData }) => {
  const [selectedProject, setSelectedProject] = useState(null)

  const getProjectColor = (project) => {
    // Extract color from Tailwind gradient classes
    const colorMap = {
      'from-red-500 to-orange-500': '#ef4444',
      'from-purple-500 to-pink-500': '#a855f7',
      'from-blue-500 to-cyan-500': '#3b82f6',
      'from-green-500 to-emerald-500': '#22c55e',
      'from-yellow-500 to-orange-500': '#eab308',
      'from-red-500 to-pink-500': '#ef4444',
      'from-indigo-500 to-purple-500': '#6366f1',
      'from-cyan-500 to-blue-500': '#06b6d4',
    }
    return colorMap[project.color] || '#7c77c6'
  }

  const getCategoryIcon = (project) => {
    // Use the icon from the project data
    const IconComponent = project.categoryIcon
    return IconComponent ? <IconComponent size={16} /> : '🚀'
  }

  const getStatusIcon = (status) => {
    switch (status.toLowerCase()) {
      case 'live':
      case 'production ready':
      case 'public release': return '🟢'
      case 'completed': return '✅'
      case 'in progress':
      case 'active development':
      case 'r&d phase': return '🔄'
      default: return '⭐'
    }
  }

  return (
    <div className="projects-showcase">
      {/* Projects Timeline */}
      <div className="projects-timeline">
        <div className="timeline-line"></div>

        {projectsData.map((project, index) => (
          <motion.div
            key={project.id}
            className={`timeline-project ${index % 2 === 0 ? 'left' : 'right'}`}
            initial={{ opacity: 0, x: index % 2 === 0 ? -100 : 100 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: index * 0.2 }}
            viewport={{ once: true }}
          >
            {/* Timeline Node */}
            <div className="timeline-node-container">
              <motion.div
                className="timeline-node"
                style={{ backgroundColor: getProjectColor(project) }}
                initial={{ scale: 0 }}
                whileInView={{ scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.2 + 0.3 }}
                whileHover={{ scale: 1.2 }}
              >
                <span className="node-number">{index + 1}</span>
              </motion.div>
              <div className="timeline-connector" style={{ backgroundColor: getProjectColor(project) }}></div>
            </div>

            {/* Project Card */}
            <motion.div
              className="project-card-timeline"
              style={{ borderColor: getProjectColor(project) }}
              whileHover={{ y: -10, scale: 1.02 }}
              onClick={() => setSelectedProject(project)}
            >
              <div className="project-header-timeline">
                <div className="project-status-timeline">
                  <span className="status-indicator" style={{ backgroundColor: getProjectColor(project) }}>
                    {getStatusIcon(project.status)}
                  </span>
                  <span className="status-text">{project.status}</span>
                </div>
                <div className="project-category-timeline">
                  {getCategoryIcon(project)} {project.category}
                </div>
              </div>

              <div className="project-title-section">
                <h3 className="project-title-timeline">{project.title}</h3>
                <p className="project-subtitle-timeline">{project.subtitle}</p>
              </div>

              <p className="project-description-timeline">{project.description}</p>

              <div className="project-features-timeline">
                {project.features.slice(0, 3).map((feature, i) => (
                  <div key={i} className="feature-timeline">
                    <span className="feature-bullet" style={{ backgroundColor: getProjectColor(project) }}>•</span>
                    {feature}
                  </div>
                ))}
              </div>

              <div className="project-tech-timeline">
                {project.technologies.slice(0, 5).map((tech, i) => (
                  <span
                    key={i}
                    className="tech-tag-timeline"
                    style={{
                      backgroundColor: `${getProjectColor(project)}20`,
                      borderColor: getProjectColor(project),
                      color: getProjectColor(project)
                    }}
                  >
                    {tech}
                  </span>
                ))}
                {project.technologies.length > 5 && (
                  <span className="tech-more-timeline">+{project.technologies.length - 5}</span>
                )}
              </div>

              <div className="project-actions-timeline">
                {project.demo && (
                  <a
                    href={project.demo}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="action-btn demo"
                    style={{ backgroundColor: getProjectColor(project) }}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <ExternalLink size={14} />
                    Demo
                  </a>
                )}
                {project.github && (
                  <a
                    href={project.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="action-btn github"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Github size={14} />
                    Code
                  </a>
                )}
              </div>
            </motion.div>
          </motion.div>
        ))}
      </div>

      {/* Project Modal */}
      <AnimatePresence>
        {selectedProject && (
          <motion.div
            className="project-modal-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedProject(null)}
          >
            <motion.div
              className="project-modal-timeline"
              initial={{ scale: 0.8, opacity: 0, y: 50 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.8, opacity: 0, y: 50 }}
              onClick={(e) => e.stopPropagation()}
            >
              <button
                className="modal-close-timeline"
                onClick={() => setSelectedProject(null)}
              >
                <X size={24} />
              </button>

              <div className="modal-content-timeline">
                <div className="modal-header-timeline">
                  <h2>{selectedProject.title}</h2>
                  <p className="modal-subtitle">{selectedProject.subtitle}</p>
                  <div className="modal-meta">
                    <span className="modal-category">
                      {getCategoryIcon(selectedProject)} {selectedProject.category}
                    </span>
                    <span className="modal-status">
                      {getStatusIcon(selectedProject.status)} {selectedProject.status}
                    </span>
                    <span className="modal-period">
                      📅 {selectedProject.period}
                    </span>
                  </div>
                </div>

                <p className="modal-description-timeline">{selectedProject.longDescription || selectedProject.description}</p>

                <div className="modal-features-timeline">
                  <h4><Award size={16} /> Key Features</h4>
                  <div className="features-grid">
                    {selectedProject.features.map((feature, i) => (
                      <div key={i} className="feature-item-modal">
                        <span className="feature-check">✓</span>
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>

                {selectedProject.outcomes && (
                  <div className="modal-outcomes-timeline">
                    <h4>🎯 Key Outcomes</h4>
                    <div className="outcomes-grid">
                      {selectedProject.outcomes.map((outcome, i) => (
                        <div key={i} className="outcome-item-modal">
                          <span className="outcome-check">🚀</span>
                          {outcome}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="modal-technologies-timeline">
                  <h4><Code size={16} /> Technologies Used</h4>
                  <div className="tech-grid-modal">
                    {selectedProject.technologies.map((tech, i) => (
                      <span key={i} className="tech-item-modal-timeline">{tech}</span>
                    ))}
                  </div>
                </div>

                {selectedProject.techDetails && (
                  <div className="modal-tech-details">
                    <h4>⚙️ Technical Details</h4>
                    <div className="tech-details-grid">
                      <div className="tech-detail-item">
                        <strong>Architecture:</strong> {selectedProject.techDetails.architecture}
                      </div>
                      {selectedProject.techDetails.database && (
                        <div className="tech-detail-item">
                          <strong>Database:</strong> {selectedProject.techDetails.database}
                        </div>
                      )}
                      {selectedProject.techDetails.deployment && (
                        <div className="tech-detail-item">
                          <strong>Deployment:</strong> {selectedProject.techDetails.deployment}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="modal-actions-timeline">
                  {selectedProject.liveLinks && selectedProject.liveLinks.length > 0 ? (
                    selectedProject.liveLinks.map((link, i) => (
                      <a
                        key={i}
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="modal-btn-timeline primary"
                        title={link.description}
                      >
                        <ExternalLink size={16} />
                        {link.name}
                      </a>
                    ))
                  ) : (
                    <>
                      {selectedProject.demo && (
                        <a
                          href={selectedProject.demo}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="modal-btn-timeline primary"
                        >
                          <ExternalLink size={16} />
                          Live Demo
                        </a>
                      )}
                      {selectedProject.github && (
                        <a
                          href={selectedProject.github}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="modal-btn-timeline secondary"
                        >
                          <Github size={16} />
                          View Code
                        </a>
                      )}
                    </>
                  )}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default ProjectsShowcase
