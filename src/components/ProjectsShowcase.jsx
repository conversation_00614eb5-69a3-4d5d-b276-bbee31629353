import { motion, AnimatePresence } from 'framer-motion'
import { useState } from 'react'
import { Github, ExternalLink, X, Code, Award, Calendar, MapPin } from 'lucide-react'

const ProjectsShowcase = ({ projectsData }) => {
  const [selectedProject, setSelectedProject] = useState(null)

  const getProjectColor = (index) => {
    const colors = [
      '#ff6b6b', // Red
      '#4ecdc4', // Teal
      '#45b7d1', // Blue
      '#96ceb4', // Green
      '#feca57', // Yellow
      '#ff9ff3', // Pink
      '#54a0ff', // Light Blue
      '#5f27cd', // Purple
    ]
    return colors[index % colors.length]
  }

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'AI/ML': return '🤖'
      case 'Full Stack': return '🌐'
      case 'Mobile': return '📱'
      case 'Web': return '💻'
      default: return '🚀'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'live': return '🟢'
      case 'completed': return '✅'
      case 'in-progress': return '🔄'
      default: return '⭐'
    }
  }

  return (
    <div className="projects-showcase">
      {/* Projects Timeline */}
      <div className="projects-timeline">
        <div className="timeline-line"></div>

        {projectsData.map((project, index) => (
          <motion.div
            key={project.id}
            className={`timeline-project ${index % 2 === 0 ? 'left' : 'right'}`}
            initial={{ opacity: 0, x: index % 2 === 0 ? -100 : 100 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: index * 0.2 }}
            viewport={{ once: true }}
          >
            {/* Timeline Node */}
            <div className="timeline-node-container">
              <motion.div
                className="timeline-node"
                style={{ backgroundColor: getProjectColor(index) }}
                initial={{ scale: 0 }}
                whileInView={{ scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.2 + 0.3 }}
                whileHover={{ scale: 1.2 }}
              >
                <span className="node-number">{index + 1}</span>
              </motion.div>
              <div className="timeline-connector" style={{ backgroundColor: getProjectColor(index) }}></div>
            </div>

            {/* Project Card */}
            <motion.div
              className="project-card-timeline"
              style={{ borderColor: getProjectColor(index) }}
              whileHover={{ y: -10, scale: 1.02 }}
              onClick={() => setSelectedProject(project)}
            >
              <div className="project-header-timeline">
                <div className="project-status-timeline">
                  <span className="status-indicator" style={{ backgroundColor: getProjectColor(index) }}>
                    {getStatusIcon(project.status)}
                  </span>
                  <span className="status-text">{project.status}</span>
                </div>
                <div className="project-category-timeline">
                  {getCategoryIcon(project.category)} {project.category}
                </div>
              </div>

              <h3 className="project-title-timeline">{project.title}</h3>

              <p className="project-description-timeline">{project.description}</p>

              <div className="project-features-timeline">
                {project.features.slice(0, 3).map((feature, i) => (
                  <div key={i} className="feature-timeline">
                    <span className="feature-bullet" style={{ backgroundColor: getProjectColor(index) }}>•</span>
                    {feature}
                  </div>
                ))}
              </div>

              <div className="project-tech-timeline">
                {project.technologies.slice(0, 5).map((tech, i) => (
                  <span
                    key={i}
                    className="tech-tag-timeline"
                    style={{
                      backgroundColor: `${getProjectColor(index)}20`,
                      borderColor: getProjectColor(index),
                      color: getProjectColor(index)
                    }}
                  >
                    {tech}
                  </span>
                ))}
                {project.technologies.length > 5 && (
                  <span className="tech-more-timeline">+{project.technologies.length - 5}</span>
                )}
              </div>

              <div className="project-actions-timeline">
                {project.demo && (
                  <a
                    href={project.demo}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="action-btn demo"
                    style={{ backgroundColor: getProjectColor(index) }}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <ExternalLink size={14} />
                    Demo
                  </a>
                )}
                {project.github && (
                  <a
                    href={project.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="action-btn github"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Github size={14} />
                    Code
                  </a>
                )}
              </div>
            </motion.div>
          </motion.div>
        ))}
      </div>

      {/* Project Modal */}
      <AnimatePresence>
        {selectedProject && (
          <motion.div
            className="project-modal-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedProject(null)}
          >
            <motion.div
              className="project-modal-timeline"
              initial={{ scale: 0.8, opacity: 0, y: 50 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.8, opacity: 0, y: 50 }}
              onClick={(e) => e.stopPropagation()}
            >
              <button
                className="modal-close-timeline"
                onClick={() => setSelectedProject(null)}
              >
                <X size={24} />
              </button>

              <div className="modal-content-timeline">
                <div className="modal-header-timeline">
                  <h2>{selectedProject.title}</h2>
                  <div className="modal-meta">
                    <span className="modal-category">
                      {getCategoryIcon(selectedProject.category)} {selectedProject.category}
                    </span>
                    <span className="modal-status">
                      {getStatusIcon(selectedProject.status)} {selectedProject.status}
                    </span>
                  </div>
                </div>

                <p className="modal-description-timeline">{selectedProject.description}</p>

                <div className="modal-features-timeline">
                  <h4><Award size={16} /> Key Features</h4>
                  <div className="features-grid">
                    {selectedProject.features.map((feature, i) => (
                      <div key={i} className="feature-item-modal">
                        <span className="feature-check">✓</span>
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>

                <div className="modal-technologies-timeline">
                  <h4><Code size={16} /> Technologies Used</h4>
                  <div className="tech-grid-modal">
                    {selectedProject.technologies.map((tech, i) => (
                      <span key={i} className="tech-item-modal-timeline">{tech}</span>
                    ))}
                  </div>
                </div>

                <div className="modal-actions-timeline">
                  {selectedProject.demo && (
                    <a
                      href={selectedProject.demo}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="modal-btn-timeline primary"
                    >
                      <ExternalLink size={16} />
                      Live Demo
                    </a>
                  )}
                  {selectedProject.github && (
                    <a
                      href={selectedProject.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="modal-btn-timeline secondary"
                    >
                      <Github size={16} />
                      View Code
                    </a>
                  )}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default ProjectsShowcase
