import { motion, AnimatePresence } from 'framer-motion'
import { useState } from 'react'
import { Github, ExternalLink, Search, Filter, X, Calendar, Code, Award, TrendingUp } from 'lucide-react'

const ProjectsShowcase = ({ projectsData }) => {
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedProject, setSelectedProject] = useState(null)

  // Get unique categories
  const categories = ['All', ...new Set(projectsData.map(project => project.category))]

  // Filter projects based on category and search term
  const filteredProjects = projectsData.filter(project => {
    const matchesCategory = selectedCategory === 'All' || project.category === selectedCategory
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.technologies.some(tech => tech.toLowerCase().includes(searchTerm.toLowerCase()))
    return matchesCategory && matchesSearch
  })

  const getStatusColor = (status) => {
    switch (status) {
      case 'live': return '#22c55e'
      case 'completed': return '#3b82f6'
      case 'in-progress': return '#f59e0b'
      default: return '#6b7280'
    }
  }

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'AI/ML': return '🤖'
      case 'Full Stack': return '🌐'
      case 'Mobile': return '📱'
      case 'Web': return '💻'
      default: return '🚀'
    }
  }

  return (
    <div className="projects-showcase">
      {/* Search and Filter Controls */}
      <div className="projects-controls">
        <div className="search-container">
          <Search size={20} />
          <input
            type="text"
            placeholder="Search projects..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-container">
          <Filter size={16} />
          <span>Filter:</span>
          <div className="category-filters">
            {categories.map((category) => (
              <motion.button
                key={category}
                className={`filter-btn ${selectedCategory === category ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {category !== 'All' && getCategoryIcon(category)} {category}
              </motion.button>
            ))}
          </div>
        </div>
      </div>

      {/* Projects Grid */}
      <motion.div 
        className="projects-grid-enhanced"
        layout
      >
        <AnimatePresence>
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              className="project-card-enhanced"
              layout
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ y: -10, scale: 1.02 }}
              onClick={() => setSelectedProject(project)}
            >
              <div className="project-image-enhanced">
                <img 
                  src={project.image} 
                  alt={project.title}
                  onError={(e) => {
                    e.target.src = `data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="400" height="300" viewBox="0 0 400 300"><rect width="400" height="300" fill="%23${Math.floor(Math.random()*16777215).toString(16)}20"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="%23ffffff" font-size="18">${project.title}</text></svg>`
                  }}
                />
                <div className="project-overlay-enhanced">
                  <span className={`project-status-enhanced ${project.status}`}>
                    {project.status === 'live' ? '🟢 Live' : project.status === 'completed' ? '✅ Completed' : '🔄 In Progress'}
                  </span>
                  <div className="project-category-badge">
                    {getCategoryIcon(project.category)} {project.category}
                  </div>
                </div>
                <div className="project-hover-overlay">
                  <span>Click to view details</span>
                </div>
              </div>

              <div className="project-content-enhanced">
                <div className="project-header-enhanced">
                  <h3>{project.title}</h3>
                  <div className="project-links-quick">
                    {project.github && (
                      <a 
                        href={project.github} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        onClick={(e) => e.stopPropagation()}
                        className="quick-link"
                      >
                        <Github size={16} />
                      </a>
                    )}
                    {project.demo && (
                      <a 
                        href={project.demo} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        onClick={(e) => e.stopPropagation()}
                        className="quick-link"
                      >
                        <ExternalLink size={16} />
                      </a>
                    )}
                  </div>
                </div>

                <p className="project-description-enhanced">{project.description}</p>

                <div className="project-features-enhanced">
                  {project.features.slice(0, 2).map((feature, i) => (
                    <div key={i} className="feature-item-enhanced">
                      <Award size={12} />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>

                <div className="project-tech-enhanced">
                  {project.technologies.slice(0, 4).map((tech, i) => (
                    <span key={i} className="tech-tag-enhanced">{tech}</span>
                  ))}
                  {project.technologies.length > 4 && (
                    <span className="tech-more">+{project.technologies.length - 4} more</span>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>

      {/* Project Modal */}
      <AnimatePresence>
        {selectedProject && (
          <motion.div
            className="project-modal-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedProject(null)}
          >
            <motion.div
              className="project-modal"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <button 
                className="modal-close"
                onClick={() => setSelectedProject(null)}
              >
                <X size={24} />
              </button>

              <div className="modal-content">
                <div className="modal-image">
                  <img src={selectedProject.image} alt={selectedProject.title} />
                  <div className="modal-status">
                    <span className={`status-badge ${selectedProject.status}`}>
                      {selectedProject.status}
                    </span>
                  </div>
                </div>

                <div className="modal-details">
                  <div className="modal-header">
                    <h2>{selectedProject.title}</h2>
                    <div className="modal-category">
                      {getCategoryIcon(selectedProject.category)} {selectedProject.category}
                    </div>
                  </div>

                  <p className="modal-description">{selectedProject.description}</p>

                  <div className="modal-features">
                    <h4><Award size={16} /> Key Features</h4>
                    <ul>
                      {selectedProject.features.map((feature, i) => (
                        <li key={i}>{feature}</li>
                      ))}
                    </ul>
                  </div>

                  <div className="modal-technologies">
                    <h4><Code size={16} /> Technologies Used</h4>
                    <div className="tech-grid">
                      {selectedProject.technologies.map((tech, i) => (
                        <span key={i} className="tech-item-modal">{tech}</span>
                      ))}
                    </div>
                  </div>

                  <div className="modal-actions">
                    {selectedProject.demo && (
                      <a 
                        href={selectedProject.demo} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="modal-btn primary"
                      >
                        <ExternalLink size={16} />
                        Live Demo
                      </a>
                    )}
                    {selectedProject.github && (
                      <a 
                        href={selectedProject.github} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="modal-btn secondary"
                      >
                        <Github size={16} />
                        View Code
                      </a>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* No Results */}
      {filteredProjects.length === 0 && (
        <motion.div 
          className="no-results"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <Search size={48} />
          <h3>No projects found</h3>
          <p>Try adjusting your search or filter criteria</p>
        </motion.div>
      )}
    </div>
  )
}

export default ProjectsShowcase
