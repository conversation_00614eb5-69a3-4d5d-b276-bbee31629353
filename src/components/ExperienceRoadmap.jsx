import { motion } from 'framer-motion'
import { Calendar, MapPin, Building, Award } from 'lucide-react'

const ExperienceRoadmap = ({ experienceData }) => {
  return (
    <div className="experience-roadmap">
      <div className="timeline-container">
        {experienceData.map((exp, index) => (
          <motion.div
            key={exp.id}
            className="timeline-item"
            initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: index * 0.2 }}
          >
            <div className="timeline-connector">
              <div className="timeline-dot">
                <Building size={20} />
              </div>
            </div>
            
            <motion.div 
              className={`timeline-content ${index % 2 === 0 ? 'left' : 'right'}`}
              whileHover={{ scale: 1.02, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <div className="experience-card-roadmap">
                <div className="experience-header-roadmap">
                  <div className="experience-title-section">
                    <h3 className="experience-title-roadmap">{exp.title}</h3>
                    <div className="experience-company-roadmap">
                      <Building size={16} />
                      <span>{exp.company}</span>
                    </div>
                  </div>
                  
                  <div className="experience-meta-roadmap">
                    <div className="meta-item">
                      <Calendar size={14} />
                      <span>{exp.period}</span>
                    </div>
                    <div className="meta-item">
                      <MapPin size={14} />
                      <span>{exp.location}</span>
                    </div>
                  </div>
                </div>

                <p className="experience-description-roadmap">{exp.description}</p>

                <div className="achievements-section">
                  <h4 className="achievements-title">
                    <Award size={16} />
                    Key Achievements
                  </h4>
                  <ul className="achievements-list-roadmap">
                    {exp.achievements.map((achievement, i) => (
                      <motion.li 
                        key={i}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: i * 0.1 }}
                      >
                        {achievement}
                      </motion.li>
                    ))}
                  </ul>
                </div>

                <div className="technologies-section">
                  <h4 className="tech-title">Technologies Used</h4>
                  <div className="tech-tags-roadmap">
                    {exp.technologies.map((tech, i) => (
                      <motion.span 
                        key={i} 
                        className="tech-tag-roadmap"
                        initial={{ opacity: 0, scale: 0 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3, delay: i * 0.05 }}
                        whileHover={{ scale: 1.1 }}
                      >
                        {tech}
                      </motion.span>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        ))}
      </div>
    </div>
  )
}

export default ExperienceRoadmap
