import { motion } from 'framer-motion'
import { useState } from 'react'
import CircularProgress from './CircularProgress'

const SkillsRoadmap = ({ skillsData }) => {
  const [activeCategory, setActiveCategory] = useState('languages')

  const categoryConfig = {
    languages: {
      title: 'Programming Languages',
      icon: '💻',
      color: '#7c77c6',
      description: 'Core programming languages I use daily'
    },
    frameworks: {
      title: 'Frameworks & Libraries',
      icon: '⚡',
      color: '#ff77c6',
      description: 'Modern frameworks for efficient development'
    },
    mlTools: {
      title: 'AI/ML Tools',
      icon: '🤖',
      color: '#77dbc6',
      description: 'Machine learning and AI technologies'
    },
    tools: {
      title: 'Development Tools',
      icon: '🛠️',
      color: '#77c6ff',
      description: 'Essential tools for development workflow'
    }
  }

  const getLevelLabel = (level) => {
    if (level >= 90) return 'Expert'
    if (level >= 80) return 'Advanced'
    if (level >= 70) return 'Intermediate'
    return 'Beginner'
  }

  const getLevelColor = (level) => {
    if (level >= 90) return '#22c55e'
    if (level >= 80) return '#3b82f6'
    if (level >= 70) return '#f59e0b'
    return '#ef4444'
  }

  return (
    <div className="skills-roadmap">
      {/* Roadmap Navigation */}
      <div className="roadmap-nav">
        <div className="roadmap-path">
          {Object.entries(categoryConfig).map(([key, config], index) => (
            <motion.div
              key={key}
              className={`roadmap-node ${activeCategory === key ? 'active' : ''}`}
              onClick={() => setActiveCategory(key)}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="node-connector" />
              <div 
                className="node-circle"
                style={{ borderColor: config.color }}
              >
                <span className="node-icon">{config.icon}</span>
              </div>
              <div className="node-label">
                <h4>{config.title}</h4>
                <p>{config.description}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Skills Content */}
      <motion.div
        className="roadmap-content"
        key={activeCategory}
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="category-header">
          <div className="category-icon" style={{ color: categoryConfig[activeCategory].color }}>
            {categoryConfig[activeCategory].icon}
          </div>
          <div className="category-info">
            <h3>{categoryConfig[activeCategory].title}</h3>
            <p>{categoryConfig[activeCategory].description}</p>
          </div>
        </div>

        <div className="skills-grid-roadmap">
          {skillsData[activeCategory]?.map((skill, index) => (
            <motion.div
              key={skill.name}
              className="skill-card-roadmap"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -8, scale: 1.02 }}
            >
              <div className="skill-progress-circle">
                <CircularProgress 
                  percentage={skill.level} 
                  size={100} 
                  strokeWidth={6}
                  delay={index * 200}
                />
              </div>
              
              <div className="skill-details">
                <div className="skill-header-roadmap">
                  <span className="skill-icon-roadmap">{skill.icon}</span>
                  <h4 className="skill-name-roadmap">{skill.name}</h4>
                </div>
                
                <div className="skill-level-info">
                  <span 
                    className="level-badge-roadmap"
                    style={{ 
                      backgroundColor: `${getLevelColor(skill.level)}20`,
                      color: getLevelColor(skill.level),
                      borderColor: getLevelColor(skill.level)
                    }}
                  >
                    {getLevelLabel(skill.level)}
                  </span>
                  <span className="skill-percentage-roadmap">{skill.level}%</span>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  )
}

export default SkillsRoadmap
