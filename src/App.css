* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: #0a0a0f;
  color: #ffffff;
  line-height: 1.6;
  overflow-x: hidden;
}

.app {
  min-height: 100vh;
  background: transparent;
  position: relative;
}

.app::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(10, 10, 15, 0.7);
  backdrop-filter: blur(25px);
  border-bottom: 1px solid rgba(124, 119, 198, 0.2);
  transition: all 0.3s ease;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7c77c6;
  font-weight: 700;
  font-size: 1.2rem;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-links a {
  color: #ffffff;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  padding: 0.5rem 0;
}

.nav-links a:hover,
.nav-links a.active {
  color: #7c77c6;
}

.nav-links a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #7c77c6, #ff77c6);
  transition: width 0.3s ease;
}

.nav-links a:hover::after,
.nav-links a.active::after {
  width: 100%;
}

.nav-cta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #7c77c6, #ff77c6);
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(124, 119, 198, 0.3);
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 0 2rem;
  position: relative;
  overflow: hidden;
}

/* Hero Decorations */
.hero-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.corner-decoration {
  position: absolute;
  width: 120px;
  height: 120px;
}

.corner-decoration.top-left {
  top: 10%;
  left: 10%;
}

.corner-decoration.top-right {
  top: 10%;
  right: 10%;
}

.corner-decoration.bottom-left {
  bottom: 10%;
  left: 10%;
}

.corner-decoration.bottom-right {
  bottom: 10%;
  right: 10%;
}

.corner-lines {
  position: relative;
  width: 100%;
  height: 100%;
}

.corner-lines .line {
  position: absolute;
  background: linear-gradient(45deg, #7c77c6, #ff77c6);
  opacity: 0.6;
}

.corner-lines .line.horizontal {
  width: 60px;
  height: 2px;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

.corner-lines .line.vertical {
  width: 2px;
  height: 60px;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}

.corner-dots {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 8px;
}

.corner-dots .dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #7c77c6;
  opacity: 0.8;
  animation: pulse 2s infinite;
}

.corner-dots .dot:nth-child(2) {
  animation-delay: 0.3s;
}

.corner-dots .dot:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes pulse {
  0%, 100% { opacity: 0.8; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

.hero-container {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.welcome-text {
  font-size: 1rem;
  color: #7c77c6;
  margin-bottom: 0.5rem;
  font-weight: 300;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.hero-title {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #7c77c6 50%, #ff77c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.5rem;
  color: #a0a0a0;
  margin-bottom: 1rem;
  font-weight: 300;
}

.hero-tagline {
  font-size: 1rem;
  color: #8892b0;
  margin-bottom: 1.5rem;
  font-style: italic;
}

.hero-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #a0a9c0;
  margin-bottom: 2rem;
  justify-content: center;
}

.hero-cta {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.cta-primary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #7c77c6, #ff77c6);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(124, 119, 198, 0.3);
}

.cta-secondary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  color: #7c77c6;
  border: 2px solid #7c77c6;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-secondary:hover {
  background: rgba(124, 119, 198, 0.1);
  transform: translateY(-2px);
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 2rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #ffffff;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.social-link:hover {
  background: rgba(124, 119, 198, 0.2);
  border-color: #7c77c6;
  transform: translateY(-2px);
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #7c77c6 50%, #ff77c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-header p {
  color: #a0a9c0;
  font-size: 1.1rem;
}

/* About Section */
.about {
  padding: 6rem 2rem;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(5px);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.about-text p {
  color: #c0c0c0;
  line-height: 1.8;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-top: 2rem;
}

.stat {
  text-align: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #7c77c6;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #a0a9c0;
  font-size: 0.9rem;
}

.about-specialties h3 {
  color: #ffffff;
  font-size: 1.5rem;
  margin-bottom: 2rem;
}

.specialties-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.specialty-item {
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.specialty-item:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  transform: translateY(-2px);
}

.specialty-icon {
  color: #7c77c6;
  margin-bottom: 1rem;
}

.specialty-item h4 {
  color: #ffffff;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.specialty-item p {
  color: #a0a9c0;
  font-size: 0.9rem;
  line-height: 1.5;
}

.timeline-container {
  max-width: 800px;
  margin: 0 auto 4rem auto;
}

.timeline {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  margin-bottom: 4rem;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #7c77c6, #ff77c6);
  transform: translateY(-50%);
  z-index: 0;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
  flex: 1;
  max-width: 150px;
}

.timeline-icon {
  width: 60px;
  height: 60px;
  background: rgba(124, 119, 198, 0.2);
  border: 2px solid #7c77c6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #7c77c6;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.timeline-icon:hover {
  background: rgba(124, 119, 198, 0.3);
  transform: scale(1.1);
}

.timeline-content {
  text-align: center;
}

.timeline-content h3 {
  font-size: 0.9rem;
  color: #ffffff;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.timeline-content p {
  font-size: 0.8rem;
  color: #a0a0a0;
  line-height: 1.4;
}

.about-description {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.about-description p {
  font-size: 1.1rem;
  color: #c0c0c0;
  line-height: 1.8;
  margin: 0;
}

/* Experience Section */
.experience {
  padding: 6rem 2rem;
}

.experience-timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.experience-timeline::before {
  content: '';
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, #7c77c6, #ff77c6);
}

.experience-item {
  position: relative;
  margin-bottom: 3rem;
  padding-left: 80px;
}

.experience-timeline-dot {
  position: absolute;
  left: 21px;
  top: 20px;
  width: 18px;
  height: 18px;
  background: #7c77c6;
  border: 4px solid #0a0a0f;
  border-radius: 50%;
  z-index: 1;
}

.experience-content {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  transition: all 0.3s ease;
}

.experience-content:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  transform: translateY(-2px);
}

.experience-header h3 {
  color: #ffffff;
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
}

.experience-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.experience-meta .company {
  color: #7c77c6;
  font-weight: 600;
}

.experience-meta .period {
  color: #a0a9c0;
}

.experience-meta .location {
  color: #8892b0;
}

.experience-description {
  color: #c0c0c0;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.experience-achievements {
  list-style: none;
  margin-bottom: 1.5rem;
}

.experience-achievements li {
  color: #a0a9c0;
  margin-bottom: 0.5rem;
  padding-left: 1rem;
  position: relative;
}

.experience-achievements li::before {
  content: '▸';
  color: #7c77c6;
  position: absolute;
  left: 0;
}

.experience-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tech-tag {
  background: rgba(124, 119, 198, 0.2);
  color: #7c77c6;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Projects Section */
.projects {
  padding: 6rem 2rem;
  background: rgba(255, 255, 255, 0.02);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.project-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.project-card:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  box-shadow: 0 10px 30px rgba(124, 119, 198, 0.2);
}

.project-image {
  position: relative;
  height: 200px;
  background: linear-gradient(135deg, #1a1a2e, #16213e);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.project-overlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.project-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.project-status.live {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.project-status.completed {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.project-content {
  padding: 2rem;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-bottom: 1rem;
}

.project-header h3 {
  color: #ffffff;
  font-size: 1.3rem;
  font-weight: 600;
}

.project-category {
  background: rgba(124, 119, 198, 0.2);
  color: #7c77c6;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.project-content p {
  color: #a0a9c0;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.project-features {
  margin-bottom: 1.5rem;
}

.feature-item {
  display: block;
  color: #8892b0;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.project-links {
  display: flex;
  gap: 1rem;
}

.project-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7c77c6;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.project-link:hover {
  color: #ff77c6;
  transform: translateY(-1px);
}

.project-link.primary {
  background: linear-gradient(135deg, #7c77c6, #ff77c6);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

.project-link.primary:hover {
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(124, 119, 198, 0.3);
}

/* Technologies Section */
.technologies {
  padding: 6rem 2rem;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(5px);
  overflow: hidden;
}

.technologies-slider {
  width: 100%;
  overflow: hidden;
  margin: 3rem 0;
  position: relative;
}

.technologies-track {
  display: flex;
  gap: 3rem;
  animation: scroll-left 30s linear infinite;
  width: fit-content;
}

.tech-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  min-width: 120px;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.tech-item:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  transform: translateY(-5px);
}

.tech-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  padding: 10px;
}

.tech-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: brightness(1.1);
}

.tech-name {
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
}

.tech-note {
  text-align: center;
  margin-top: 2rem;
}

.tech-note p {
  color: #a0a9c0;
  font-style: italic;
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Pause animation on hover */
.technologies-slider:hover .technologies-track {
  animation-play-state: paused;
}

/* Responsive adjustments for technologies */
@media (max-width: 768px) {
  .technologies-track {
    gap: 2rem;
    animation-duration: 25s;
  }

  .tech-item {
    min-width: 100px;
    padding: 1rem;
  }

  .tech-icon {
    width: 50px;
    height: 50px;
  }

  .tech-name {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .technologies-track {
    gap: 1.5rem;
    animation-duration: 20s;
  }

  .tech-item {
    min-width: 80px;
    padding: 0.8rem;
  }

  .tech-icon {
    width: 40px;
    height: 40px;
  }

  .tech-name {
    font-size: 0.7rem;
  }
}

/* Skills Section */
.skills {
  padding: 6rem 2rem;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.skills-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Skills Navigation */
.skills-nav {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: sticky;
  top: 120px;
  height: fit-content;
}

.skill-nav-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  color: #a0a9c0;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  backdrop-filter: blur(10px);
}

.skill-nav-btn:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  color: #ffffff;
  transform: translateX(5px);
}

.skill-nav-btn.active {
  background: linear-gradient(135deg, rgba(124, 119, 198, 0.2), rgba(255, 119, 198, 0.1));
  border-color: #7c77c6;
  color: #ffffff;
  box-shadow: 0 8px 25px rgba(124, 119, 198, 0.2);
}

.nav-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.nav-text {
  font-weight: 600;
  font-size: 1rem;
}

/* Skills Content */
.skills-content {
  min-height: 400px;
}

.skills-grid-advanced {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
}

.skill-card-advanced {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.4s ease;
  cursor: pointer;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.skill-card-advanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #7c77c6, #ff77c6);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.skill-card-advanced:hover::before {
  transform: scaleX(1);
}

.skill-card-advanced:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  box-shadow: 0 15px 35px rgba(124, 119, 198, 0.2);
}

.skill-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.skill-icon-wrapper {
  width: 60px;
  height: 60px;
  background: rgba(124, 119, 198, 0.1);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(124, 119, 198, 0.2);
}

.skill-icon-wrapper .skill-icon {
  font-size: 2rem;
}

.skill-info {
  flex: 1;
}

.skill-info .skill-name {
  color: #ffffff;
  font-weight: 700;
  font-size: 1.2rem;
  margin-bottom: 0.25rem;
}

.skill-info .skill-percentage {
  color: #7c77c6;
  font-size: 1.1rem;
  font-weight: 600;
}

.skill-progress-container {
  margin-bottom: 1.5rem;
}

.skill-bar-advanced {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.skill-progress-advanced {
  height: 100%;
  background: linear-gradient(90deg, #7c77c6, #ff77c6);
  border-radius: 10px;
  position: relative;
}

.skill-progress-advanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.skill-level-text {
  display: flex;
  justify-content: flex-end;
}

.level-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.level-badge.expert {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.level-badge.advanced {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.level-badge.intermediate {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.level-badge.beginner {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Certifications Section */
.certifications {
  padding: 6rem 2rem;
  background: rgba(255, 255, 255, 0.02);
}

.certifications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.certification-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.3s ease;
}

.certification-card:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  transform: translateY(-5px);
}

.certification-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.certification-icon {
  color: #7c77c6;
  flex-shrink: 0;
}

.certification-meta h3 {
  color: #ffffff;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.certification-issuer {
  color: #7c77c6;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.certification-date {
  color: #a0a9c0;
  font-size: 0.9rem;
}

.certification-description {
  color: #c0c0c0;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.certification-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.skill-tag {
  background: rgba(124, 119, 198, 0.2);
  color: #7c77c6;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.certification-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7c77c6;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.certification-link:hover {
  color: #ff77c6;
  transform: translateY(-1px);
}

/* Code Showcase */
.code-showcase {
  padding: 6rem 2rem;
}

.code-window {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(20, 20, 30, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.code-header {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.code-dots {
  display: flex;
  gap: 0.5rem;
  margin-right: 1rem;
}

.code-dots span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ff5f56;
}

.code-dots span:nth-child(2) {
  background: #ffbd2e;
}

.code-dots span:nth-child(3) {
  background: #27ca3f;
}

.code-title {
  color: #a0a0a0;
  font-size: 0.9rem;
}

.code-content {
  padding: 2rem;
}

.code-content pre {
  color: #e0e0e0;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
  overflow-x: auto;
}

/* Projects Section */
.projects {
  padding: 6rem 2rem;
  background: rgba(255, 255, 255, 0.02);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 4rem;
  background: linear-gradient(135deg, #ffffff 0%, #7c77c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.project-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.project-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(124, 119, 198, 0.5);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.project-image {
  height: 200px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.project-placeholder {
  color: #7c77c6;
  opacity: 0.6;
}

.project-content {
  padding: 2rem;
}

.project-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #ffffff;
}

.project-description {
  color: #c0c0c0;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.tech-tag {
  padding: 0.3rem 0.8rem;
  background: rgba(124, 119, 198, 0.2);
  border: 1px solid rgba(124, 119, 198, 0.3);
  border-radius: 20px;
  font-size: 0.8rem;
  color: #e0e0e0;
}

.project-links {
  display: flex;
  gap: 1rem;
}

.project-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #ffffff;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.project-link:hover {
  background: rgba(124, 119, 198, 0.2);
  border-color: #7c77c6;
  transform: translateY(-2px);
}

/* Contact Section */
.contact {
  padding: 6rem 2rem;
  background: rgba(255, 255, 255, 0.02);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  align-items: start;
}

.contact-info h3 {
  color: #ffffff;
  font-size: 1.8rem;
  margin-bottom: 1rem;
}

.contact-info p {
  color: #a0a9c0;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.contact-details {
  margin-bottom: 2rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.contact-item svg {
  color: #7c77c6;
  flex-shrink: 0;
}

.contact-label {
  display: block;
  color: #8892b0;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.contact-value {
  color: #ffffff;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.contact-value:hover {
  color: #7c77c6;
}

.contact-social {
  display: flex;
  gap: 1rem;
}

.contact-form-container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  text-align: left;
}

.form-group label {
  display: block;
  color: #ffffff;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #ffffff;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #a0a0a0;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #7c77c6;
  background: rgba(255, 255, 255, 0.08);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

.form-submit {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #7c77c6 0%, #ff77c6 100%);
  border: none;
  border-radius: 12px;
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.form-submit:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(124, 119, 198, 0.4);
}

/* Footer */
.footer {
  padding: 2rem;
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.footer-content p {
  color: #a0a9c0;
  margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .specialties-grid {
    grid-template-columns: 1fr;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .nav-cta {
    display: none;
  }

  .hero-title {
    font-size: 3rem;
  }

  .hero-cta {
    flex-direction: column;
    align-items: center;
  }

  .cta-primary,
  .cta-secondary {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .about-stats {
    grid-template-columns: 1fr;
  }

  .experience-item {
    padding-left: 60px;
  }

  .experience-timeline::before {
    left: 20px;
  }

  .experience-timeline-dot {
    left: 11px;
  }

  .skills-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .skills-nav {
    position: static;
    flex-direction: row;
    overflow-x: auto;
    gap: 1rem;
    padding-bottom: 1rem;
  }

  .skill-nav-btn {
    min-width: 150px;
    flex-shrink: 0;
  }

  .skills-grid-advanced {
    grid-template-columns: 1fr;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .certifications-grid {
    grid-template-columns: 1fr;
  }

  .project-links {
    flex-direction: column;
  }

  .project-link {
    justify-content: center;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .contact-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 0 1rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .skills,
  .code-showcase,
  .projects,
  .contact {
    padding: 4rem 1rem;
  }

  .nav-container {
    padding: 1rem;
  }

  .experience-item {
    padding-left: 40px;
  }

  .experience-timeline::before {
    left: 10px;
  }

  .experience-timeline-dot {
    left: 1px;
    width: 16px;
    height: 16px;
  }

  .skills-nav {
    flex-direction: row;
    gap: 0.5rem;
  }

  .skill-nav-btn {
    min-width: 120px;
    padding: 1rem;
    font-size: 0.9rem;
  }

  .nav-icon {
    font-size: 1.2rem;
  }

  .skill-card-advanced {
    padding: 1.5rem;
  }

  .skill-header {
    gap: 1rem;
  }

  .skill-icon-wrapper {
    width: 50px;
    height: 50px;
  }

  .skill-icon-wrapper .skill-icon {
    font-size: 1.5rem;
  }
}
