* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: #0a0a0f;
  color: #ffffff;
  line-height: 1.6;
  overflow-x: hidden;
}

.app {
  min-height: 100vh;
  background: transparent;
  position: relative;
}

/* Removed gradient background since we're using video background */

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(10, 10, 15, 0.7);
  backdrop-filter: blur(25px);
  border-bottom: 1px solid rgba(124, 119, 198, 0.2);
  transition: all 0.3s ease;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7c77c6;
  font-weight: 700;
  font-size: 1.2rem;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-links a {
  color: #ffffff;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  padding: 0.5rem 0;
}

.nav-links a:hover,
.nav-links a.active {
  color: #7c77c6;
}

.nav-links a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #7c77c6, #ff77c6);
  transition: width 0.3s ease;
}

.nav-links a:hover::after,
.nav-links a.active::after {
  width: 100%;
}

.nav-cta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #7c77c6, #ff77c6);
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(124, 119, 198, 0.3);
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 0 2rem;
  position: relative;
  overflow: hidden;
}

/* Hero Decorations */
.hero-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.corner-decoration {
  position: absolute;
  width: 120px;
  height: 120px;
}

.corner-decoration.top-left {
  top: 10%;
  left: 10%;
}

.corner-decoration.top-right {
  top: 10%;
  right: 10%;
}

.corner-decoration.bottom-left {
  bottom: 10%;
  left: 10%;
}

.corner-decoration.bottom-right {
  bottom: 10%;
  right: 10%;
}

.corner-lines {
  position: relative;
  width: 100%;
  height: 100%;
}

.corner-lines .line {
  position: absolute;
  background: linear-gradient(45deg, #7c77c6, #ff77c6);
  opacity: 0.6;
}

.corner-lines .line.horizontal {
  width: 60px;
  height: 2px;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

.corner-lines .line.vertical {
  width: 2px;
  height: 60px;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}

.corner-dots {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 8px;
}

.corner-dots .dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #7c77c6;
  opacity: 0.8;
  animation: pulse 2s infinite;
}

.corner-dots .dot:nth-child(2) {
  animation-delay: 0.3s;
}

.corner-dots .dot:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes pulse {
  0%, 100% { opacity: 0.8; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

.hero-container {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.welcome-text {
  font-size: 1rem;
  color: #7c77c6;
  margin-bottom: 0.5rem;
  font-weight: 300;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.hero-title {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #7c77c6 50%, #ff77c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.5rem;
  color: #a0a0a0;
  margin-bottom: 1rem;
  font-weight: 300;
}

.hero-tagline {
  font-size: 1rem;
  color: #8892b0;
  margin-bottom: 1.5rem;
  font-style: italic;
}

.hero-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #a0a9c0;
  margin-bottom: 2rem;
  justify-content: center;
}

.hero-cta {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.cta-primary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #7c77c6, #ff77c6);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(124, 119, 198, 0.3);
}

.cta-secondary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  color: #7c77c6;
  border: 2px solid #7c77c6;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-secondary:hover {
  background: rgba(124, 119, 198, 0.1);
  transform: translateY(-2px);
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 2rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #ffffff;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.social-link:hover {
  background: rgba(124, 119, 198, 0.2);
  border-color: #7c77c6;
  transform: translateY(-2px);
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #7c77c6 50%, #ff77c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-header p {
  color: #a0a9c0;
  font-size: 1.1rem;
}

/* About Section */
.about {
  padding: 6rem 2rem;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(5px);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.about-text p {
  color: #c0c0c0;
  line-height: 1.8;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-top: 2rem;
}

.stat {
  text-align: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #7c77c6;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #a0a9c0;
  font-size: 0.9rem;
}

.about-specialties h3 {
  color: #ffffff;
  font-size: 1.5rem;
  margin-bottom: 2rem;
}

.specialties-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.specialty-item {
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.specialty-item:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  transform: translateY(-2px);
}

.specialty-icon {
  color: #7c77c6;
  margin-bottom: 1rem;
}

.specialty-item h4 {
  color: #ffffff;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.specialty-item p {
  color: #a0a9c0;
  font-size: 0.9rem;
  line-height: 1.5;
}

.timeline-container {
  max-width: 800px;
  margin: 0 auto 4rem auto;
}

.timeline {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  margin-bottom: 4rem;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #7c77c6, #ff77c6);
  transform: translateY(-50%);
  z-index: 0;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
  flex: 1;
  max-width: 150px;
}

.timeline-icon {
  width: 60px;
  height: 60px;
  background: rgba(124, 119, 198, 0.2);
  border: 2px solid #7c77c6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #7c77c6;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.timeline-icon:hover {
  background: rgba(124, 119, 198, 0.3);
  transform: scale(1.1);
}

.timeline-content {
  text-align: center;
}

.timeline-content h3 {
  font-size: 0.9rem;
  color: #ffffff;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.timeline-content p {
  font-size: 0.8rem;
  color: #a0a0a0;
  line-height: 1.4;
}

.about-description {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.about-description p {
  font-size: 1.1rem;
  color: #c0c0c0;
  line-height: 1.8;
  margin: 0;
}

/* Experience Section */
.experience {
  padding: 6rem 2rem;
}

/* Experience Roadmap */
.experience-roadmap {
  max-width: 1200px;
  margin: 0 auto;
}

.timeline-container {
  position: relative;
  padding: 2rem 0;
}

.timeline-container::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #7c77c6, #ff77c6, #77dbc6, #77c6ff);
  transform: translateX(-50%);
  border-radius: 2px;
}

.timeline-item {
  position: relative;
  margin-bottom: 4rem;
  display: flex;
  align-items: center;
}

.timeline-connector {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.timeline-dot {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #7c77c6, #ff77c6);
  border: 4px solid #0a0a0f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  box-shadow: 0 0 20px rgba(124, 119, 198, 0.4);
}

.timeline-content {
  width: 45%;
  position: relative;
}

.timeline-content.left {
  margin-right: auto;
  padding-right: 3rem;
}

.timeline-content.right {
  margin-left: auto;
  padding-left: 3rem;
}

.experience-card-roadmap {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2.5rem;
  backdrop-filter: blur(10px);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.experience-card-roadmap::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #7c77c6, #ff77c6);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.experience-card-roadmap:hover::before {
  transform: scaleX(1);
}

.experience-card-roadmap:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  box-shadow: 0 20px 40px rgba(124, 119, 198, 0.2);
}

.experience-header-roadmap {
  margin-bottom: 1.5rem;
}

.experience-title-section {
  margin-bottom: 1rem;
}

.experience-title-roadmap {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.experience-company-roadmap {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7c77c6;
  font-weight: 600;
  font-size: 1.1rem;
}

.experience-meta-roadmap {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #a0a9c0;
  font-size: 0.9rem;
}

.experience-description-roadmap {
  color: #c0c0c0;
  line-height: 1.7;
  margin-bottom: 2rem;
  font-size: 1rem;
}

.achievements-section {
  margin-bottom: 2rem;
}

.achievements-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.achievements-list-roadmap {
  list-style: none;
  padding: 0;
}

.achievements-list-roadmap li {
  color: #a0a9c0;
  margin-bottom: 0.8rem;
  padding-left: 1.5rem;
  position: relative;
  line-height: 1.6;
}

.achievements-list-roadmap li::before {
  content: '▸';
  color: #7c77c6;
  position: absolute;
  left: 0;
  font-weight: bold;
}

.technologies-section {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1.5rem;
}

.tech-title {
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.tech-tags-roadmap {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

.tech-tag-roadmap {
  background: rgba(124, 119, 198, 0.2);
  color: #7c77c6;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid rgba(124, 119, 198, 0.3);
  transition: all 0.3s ease;
}

.tech-tag-roadmap:hover {
  background: rgba(124, 119, 198, 0.3);
  transform: translateY(-2px);
}

/* Projects Section */
.projects {
  padding: 6rem 2rem;
  background: rgba(255, 255, 255, 0.02);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.project-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.project-card:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  box-shadow: 0 10px 30px rgba(124, 119, 198, 0.2);
}

.project-image {
  position: relative;
  height: 200px;
  background: linear-gradient(135deg, #1a1a2e, #16213e);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.project-overlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.project-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.project-status.live {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.project-status.completed {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.project-content {
  padding: 2rem;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-bottom: 1rem;
}

.project-header h3 {
  color: #ffffff;
  font-size: 1.3rem;
  font-weight: 600;
}

.project-category {
  background: rgba(124, 119, 198, 0.2);
  color: #7c77c6;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.project-content p {
  color: #a0a9c0;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.project-features {
  margin-bottom: 1.5rem;
}

.feature-item {
  display: block;
  color: #8892b0;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.project-links {
  display: flex;
  gap: 1rem;
}

.project-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7c77c6;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.project-link:hover {
  color: #ff77c6;
  transform: translateY(-1px);
}

.project-link.primary {
  background: linear-gradient(135deg, #7c77c6, #ff77c6);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

.project-link.primary:hover {
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(124, 119, 198, 0.3);
}

/* Projects Timeline Showcase */
.projects-showcase {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 0;
}

.projects-timeline {
  position: relative;
  padding: 2rem 0;
}

.timeline-line {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg,
    #ff6b6b 0%,
    #4ecdc4 20%,
    #45b7d1 40%,
    #96ceb4 60%,
    #feca57 80%,
    #ff9ff3 100%
  );
  transform: translateX(-50%);
  border-radius: 2px;
  box-shadow: 0 0 20px rgba(124, 119, 198, 0.3);
}

.timeline-project {
  position: relative;
  margin-bottom: 6rem;
  display: flex;
  align-items: center;
}

.timeline-project.left {
  justify-content: flex-end;
  padding-right: 3rem;
}

.timeline-project.right {
  justify-content: flex-start;
  padding-left: 3rem;
}

.timeline-node-container {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.timeline-node {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-weight: bold;
  font-size: 1.2rem;
  border: 4px solid #0a0a0f;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.timeline-node:hover {
  box-shadow: 0 0 40px rgba(255, 255, 255, 0.3);
}

.timeline-connector {
  width: 2px;
  height: 30px;
  margin-top: 5px;
}

.project-card-timeline {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid;
  border-radius: 20px;
  padding: 2rem;
  max-width: 450px;
  width: 100%;
  backdrop-filter: blur(10px);
  transition: all 0.4s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.project-card-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: inherit;
  border-color: inherit;
}

.project-card-timeline:hover {
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.project-header-timeline {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.project-status-timeline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
}

.status-text {
  color: #a0a9c0;
  font-size: 0.9rem;
  text-transform: capitalize;
  font-weight: 500;
}

.project-category-timeline {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  color: #ffffff;
  font-weight: 500;
}

.project-title-timeline {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.project-description-timeline {
  color: #c0c0c0;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

.project-features-timeline {
  margin-bottom: 1.5rem;
}

.feature-timeline {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 0.5rem;
  color: #a0a9c0;
  font-size: 0.9rem;
}

.feature-bullet {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.project-tech-timeline {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.tech-tag-timeline {
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid;
  transition: all 0.3s ease;
}

.tech-tag-timeline:hover {
  transform: translateY(-2px);
}

.tech-more-timeline {
  color: #7c77c6;
  font-size: 0.8rem;
  font-weight: 500;
  padding: 0.3rem 0.8rem;
}

.project-actions-timeline {
  display: flex;
  gap: 1rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1.2rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.action-btn.demo {
  color: #ffffff;
}

.action-btn.demo:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.action-btn.github {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn.github:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Project Modal Timeline */
.project-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.project-modal-timeline {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  backdrop-filter: blur(20px);
  position: relative;
}

.modal-close-timeline {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.modal-close-timeline:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.modal-content-timeline {
  padding: 3rem;
}

.modal-header-timeline h2 {
  color: #ffffff;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.modal-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.modal-category,
.modal-status {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  color: #ffffff;
  font-weight: 500;
}

.modal-description-timeline {
  color: #c0c0c0;
  line-height: 1.7;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.modal-features-timeline h4,
.modal-technologies-timeline h4 {
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.features-grid {
  display: grid;
  gap: 0.8rem;
  margin-bottom: 2rem;
}

.feature-item-modal {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  color: #a0a9c0;
  font-size: 0.95rem;
}

.feature-check {
  color: #22c55e;
  font-weight: bold;
  font-size: 1rem;
}

.tech-grid-modal {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-bottom: 2rem;
}

.tech-item-modal-timeline {
  background: rgba(124, 119, 198, 0.2);
  color: #7c77c6;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(124, 119, 198, 0.3);
}

.modal-actions-timeline {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.modal-btn-timeline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.modal-btn-timeline.primary {
  background: linear-gradient(135deg, #7c77c6, #ff77c6);
  color: #ffffff;
}

.modal-btn-timeline.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(124, 119, 198, 0.4);
}

.modal-btn-timeline.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-btn-timeline.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Enhanced Projects Showcase */
.projects-showcase {
  max-width: 1400px;
  margin: 0 auto;
}

.projects-controls {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.search-container {
  position: relative;
  max-width: 400px;
}

.search-container svg {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #a0a9c0;
}

.search-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #7c77c6;
  background: rgba(255, 255, 255, 0.08);
}

.search-input::placeholder {
  color: #a0a9c0;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-container svg {
  color: #7c77c6;
}

.filter-container > span {
  color: #ffffff;
  font-weight: 600;
}

.category-filters {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  color: #a0a9c0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-btn:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  color: #ffffff;
}

.filter-btn.active {
  background: linear-gradient(135deg, #7c77c6, #ff77c6);
  border-color: transparent;
  color: #ffffff;
  font-weight: 600;
}

.projects-grid-enhanced {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 2rem;
}

.project-card-enhanced {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.4s ease;
  cursor: pointer;
  backdrop-filter: blur(10px);
  position: relative;
}

.project-card-enhanced:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  box-shadow: 0 20px 40px rgba(124, 119, 198, 0.2);
}

.project-image-enhanced {
  position: relative;
  height: 220px;
  overflow: hidden;
}

.project-image-enhanced img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.project-card-enhanced:hover .project-image-enhanced img {
  transform: scale(1.1);
}

.project-overlay-enhanced {
  position: absolute;
  top: 1rem;
  left: 1rem;
  right: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.project-status-enhanced {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.project-status-enhanced.live {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.project-status-enhanced.completed {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.project-category-badge {
  padding: 0.4rem 0.8rem;
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.project-hover-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: #ffffff;
  padding: 2rem 1rem 1rem;
  text-align: center;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.project-card-enhanced:hover .project-hover-overlay {
  transform: translateY(0);
}

/* Technologies Section */
.technologies {
  padding: 6rem 2rem;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(5px);
  overflow: hidden;
}

.technologies-slider {
  width: 100%;
  overflow: hidden;
  margin: 3rem 0;
  position: relative;
}

.technologies-track {
  display: flex;
  gap: 3rem;
  animation: scroll-left 30s linear infinite;
  width: fit-content;
}

.tech-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  min-width: 120px;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.tech-item:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  transform: translateY(-5px);
}

.tech-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  padding: 10px;
}

.tech-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: brightness(1.1);
}

.tech-name {
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
}

.tech-note {
  text-align: center;
  margin-top: 2rem;
}

.tech-note p {
  color: #a0a9c0;
  font-style: italic;
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Pause animation on hover */
.technologies-slider:hover .technologies-track {
  animation-play-state: paused;
}

/* Responsive adjustments for technologies */
@media (max-width: 768px) {
  .technologies-track {
    gap: 2rem;
    animation-duration: 25s;
  }

  .tech-item {
    min-width: 100px;
    padding: 1rem;
  }

  .tech-icon {
    width: 50px;
    height: 50px;
  }

  .tech-name {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .technologies-track {
    gap: 1.5rem;
    animation-duration: 20s;
  }

  .tech-item {
    min-width: 80px;
    padding: 0.8rem;
  }

  .tech-icon {
    width: 40px;
    height: 40px;
  }

  .tech-name {
    font-size: 0.7rem;
  }
}

/* Skills Section */
.skills {
  padding: 6rem 2rem;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.skills-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Skills Navigation */
.skills-nav {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: sticky;
  top: 120px;
  height: fit-content;
}

.skill-nav-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  color: #a0a9c0;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  backdrop-filter: blur(10px);
}

.skill-nav-btn:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  color: #ffffff;
  transform: translateX(5px);
}

.skill-nav-btn.active {
  background: linear-gradient(135deg, rgba(124, 119, 198, 0.2), rgba(255, 119, 198, 0.1));
  border-color: #7c77c6;
  color: #ffffff;
  box-shadow: 0 8px 25px rgba(124, 119, 198, 0.2);
}

.nav-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.nav-text {
  font-weight: 600;
  font-size: 1rem;
}

/* Skills Content */
.skills-content {
  min-height: 400px;
}

.skills-grid-advanced {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
}

.skill-card-advanced {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.4s ease;
  cursor: pointer;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.skill-card-advanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #7c77c6, #ff77c6);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.skill-card-advanced:hover::before {
  transform: scaleX(1);
}

.skill-card-advanced:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  box-shadow: 0 15px 35px rgba(124, 119, 198, 0.2);
}

.skill-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.skill-icon-wrapper {
  width: 60px;
  height: 60px;
  background: rgba(124, 119, 198, 0.1);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(124, 119, 198, 0.2);
}

.skill-icon-wrapper .skill-icon {
  font-size: 2rem;
}

.skill-info {
  flex: 1;
}

.skill-info .skill-name {
  color: #ffffff;
  font-weight: 700;
  font-size: 1.2rem;
  margin-bottom: 0.25rem;
}

.skill-info .skill-percentage {
  color: #7c77c6;
  font-size: 1.1rem;
  font-weight: 600;
}

.skill-progress-container {
  margin-bottom: 1.5rem;
}

.skill-bar-advanced {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.skill-progress-advanced {
  height: 100%;
  background: linear-gradient(90deg, #7c77c6, #ff77c6);
  border-radius: 10px;
  position: relative;
}

.skill-progress-advanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.skill-level-text {
  display: flex;
  justify-content: flex-end;
}

.level-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.level-badge.expert {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.level-badge.advanced {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.level-badge.intermediate {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.level-badge.beginner {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Circular Progress Component */
.circular-progress {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circular-progress-svg {
  transform: rotate(-90deg);
}

.circular-progress-text {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.circular-progress-text .percentage {
  font-size: 1.2rem;
  font-weight: 700;
  color: #ffffff;
}

/* Skills Roadmap */
.skills-roadmap {
  max-width: 1400px;
  margin: 0 auto;
}

/* Roadmap Navigation */
.roadmap-nav {
  margin-bottom: 4rem;
}

.roadmap-path {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  position: relative;
}

.roadmap-path::before {
  content: '';
  position: absolute;
  top: 50px;
  left: 10%;
  right: 10%;
  height: 2px;
  background: linear-gradient(90deg, #7c77c6, #ff77c6, #77dbc6, #77c6ff);
  z-index: 0;
}

.roadmap-node {
  position: relative;
  text-align: center;
  cursor: pointer;
  z-index: 1;
}

.node-circle {
  width: 100px;
  height: 100px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
}

.roadmap-node.active .node-circle {
  border-width: 4px;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 30px rgba(124, 119, 198, 0.3);
}

.roadmap-node:hover .node-circle {
  transform: scale(1.1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.node-icon {
  font-size: 2.5rem;
}

.node-label h4 {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.node-label p {
  color: #a0a9c0;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Roadmap Content */
.roadmap-content {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 24px;
  padding: 3rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.category-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.category-icon {
  font-size: 4rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.category-info h3 {
  color: #ffffff;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.category-info p {
  color: #a0a9c0;
  font-size: 1.1rem;
}

/* Skills Grid */
.skills-grid-roadmap {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.skill-card-roadmap {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  transition: all 0.4s ease;
  cursor: pointer;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.skill-card-roadmap::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #7c77c6, #ff77c6);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.skill-card-roadmap:hover::before {
  transform: scaleX(1);
}

.skill-card-roadmap:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  box-shadow: 0 15px 35px rgba(124, 119, 198, 0.2);
}

.skill-progress-circle {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
}

.skill-header-roadmap {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.skill-icon-roadmap {
  font-size: 1.8rem;
}

.skill-name-roadmap {
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
}

.skill-level-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.level-badge-roadmap {
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid;
}

.skill-percentage-roadmap {
  color: #7c77c6;
  font-size: 1.1rem;
  font-weight: 700;
}

/* Certifications Section */
.certifications {
  padding: 6rem 2rem;
  background: rgba(255, 255, 255, 0.02);
}

.certifications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.certification-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.3s ease;
}

.certification-card:hover {
  background: rgba(124, 119, 198, 0.1);
  border-color: rgba(124, 119, 198, 0.3);
  transform: translateY(-5px);
}

.certification-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.certification-icon {
  color: #7c77c6;
  flex-shrink: 0;
}

.certification-meta h3 {
  color: #ffffff;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.certification-issuer {
  color: #7c77c6;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.certification-date {
  color: #a0a9c0;
  font-size: 0.9rem;
}

.certification-description {
  color: #c0c0c0;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.certification-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.skill-tag {
  background: rgba(124, 119, 198, 0.2);
  color: #7c77c6;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.certification-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7c77c6;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.certification-link:hover {
  color: #ff77c6;
  transform: translateY(-1px);
}

/* Code Showcase */
.code-showcase {
  padding: 6rem 2rem;
}

.code-window {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(20, 20, 30, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.code-header {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.code-dots {
  display: flex;
  gap: 0.5rem;
  margin-right: 1rem;
}

.code-dots span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ff5f56;
}

.code-dots span:nth-child(2) {
  background: #ffbd2e;
}

.code-dots span:nth-child(3) {
  background: #27ca3f;
}

.code-title {
  color: #a0a0a0;
  font-size: 0.9rem;
}

.code-content {
  padding: 2rem;
}

.code-content pre {
  color: #e0e0e0;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
  overflow-x: auto;
}

/* Projects Section */
.projects {
  padding: 6rem 2rem;
  background: rgba(255, 255, 255, 0.02);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 4rem;
  background: linear-gradient(135deg, #ffffff 0%, #7c77c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.project-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.project-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(124, 119, 198, 0.5);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.project-image {
  height: 200px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.project-placeholder {
  color: #7c77c6;
  opacity: 0.6;
}

.project-content {
  padding: 2rem;
}

.project-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #ffffff;
}

.project-description {
  color: #c0c0c0;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.tech-tag {
  padding: 0.3rem 0.8rem;
  background: rgba(124, 119, 198, 0.2);
  border: 1px solid rgba(124, 119, 198, 0.3);
  border-radius: 20px;
  font-size: 0.8rem;
  color: #e0e0e0;
}

.project-links {
  display: flex;
  gap: 1rem;
}

.project-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #ffffff;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.project-link:hover {
  background: rgba(124, 119, 198, 0.2);
  border-color: #7c77c6;
  transform: translateY(-2px);
}

/* Contact Section */
.contact {
  padding: 6rem 2rem;
  background: rgba(255, 255, 255, 0.02);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  align-items: start;
}

.contact-info h3 {
  color: #ffffff;
  font-size: 1.8rem;
  margin-bottom: 1rem;
}

.contact-info p {
  color: #a0a9c0;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.contact-details {
  margin-bottom: 2rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.contact-item svg {
  color: #7c77c6;
  flex-shrink: 0;
}

.contact-label {
  display: block;
  color: #8892b0;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.contact-value {
  color: #ffffff;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.contact-value:hover {
  color: #7c77c6;
}

.contact-social {
  display: flex;
  gap: 1rem;
}

.contact-form-container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  text-align: left;
}

.form-group label {
  display: block;
  color: #ffffff;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #ffffff;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #a0a0a0;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #7c77c6;
  background: rgba(255, 255, 255, 0.08);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

.form-submit {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #7c77c6 0%, #ff77c6 100%);
  border: none;
  border-radius: 12px;
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.form-submit:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(124, 119, 198, 0.4);
}

/* Footer */
.footer {
  padding: 2rem;
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.footer-content p {
  color: #a0a9c0;
  margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .specialties-grid {
    grid-template-columns: 1fr;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .nav-cta {
    display: none;
  }

  .hero-title {
    font-size: 3rem;
  }

  .hero-cta {
    flex-direction: column;
    align-items: center;
  }

  .cta-primary,
  .cta-secondary {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .about-stats {
    grid-template-columns: 1fr;
  }

  .experience-item {
    padding-left: 60px;
  }

  .experience-timeline::before {
    left: 20px;
  }

  .experience-timeline-dot {
    left: 11px;
  }

  .skills-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .skills-nav {
    position: static;
    flex-direction: row;
    overflow-x: auto;
    gap: 1rem;
    padding-bottom: 1rem;
  }

  .skill-nav-btn {
    min-width: 150px;
    flex-shrink: 0;
  }

  .skills-grid-advanced {
    grid-template-columns: 1fr;
  }

  /* Roadmap Responsive */
  .roadmap-path {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .roadmap-path::before {
    display: none;
  }

  .node-circle {
    width: 80px;
    height: 80px;
  }

  .node-icon {
    font-size: 2rem;
  }

  .roadmap-content {
    padding: 2rem;
  }

  .skills-grid-roadmap {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  /* Experience Roadmap Mobile */
  .timeline-container::before {
    left: 30px;
    transform: none;
  }

  .timeline-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .timeline-connector {
    left: 30px;
    transform: none;
  }

  .timeline-dot {
    width: 50px;
    height: 50px;
  }

  .timeline-content {
    width: 100%;
    padding-left: 80px !important;
    padding-right: 0 !important;
  }

  .experience-card-roadmap {
    padding: 2rem;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .certifications-grid {
    grid-template-columns: 1fr;
  }

  .project-links {
    flex-direction: column;
  }

  .project-link {
    justify-content: center;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .contact-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 0 1rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .skills,
  .code-showcase,
  .projects,
  .contact {
    padding: 4rem 1rem;
  }

  .nav-container {
    padding: 1rem;
  }

  .experience-item {
    padding-left: 40px;
  }

  .experience-timeline::before {
    left: 10px;
  }

  .experience-timeline-dot {
    left: 1px;
    width: 16px;
    height: 16px;
  }

  .skills-nav {
    flex-direction: row;
    gap: 0.5rem;
  }

  .skill-nav-btn {
    min-width: 120px;
    padding: 1rem;
    font-size: 0.9rem;
  }

  .nav-icon {
    font-size: 1.2rem;
  }

  .skill-card-advanced {
    padding: 1.5rem;
  }

  .skill-header {
    gap: 1rem;
  }

  .skill-icon-wrapper {
    width: 50px;
    height: 50px;
  }

  .skill-icon-wrapper .skill-icon {
    font-size: 1.5rem;
  }

  /* Roadmap Mobile */
  .roadmap-path {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .node-circle {
    width: 70px;
    height: 70px;
  }

  .node-icon {
    font-size: 1.8rem;
  }

  .node-label h4 {
    font-size: 1rem;
  }

  .node-label p {
    font-size: 0.8rem;
  }

  .roadmap-content {
    padding: 1.5rem;
  }

  .category-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .category-icon {
    font-size: 3rem;
    width: 70px;
    height: 70px;
  }

  .skills-grid-roadmap {
    grid-template-columns: 1fr;
  }

  .skill-card-roadmap {
    padding: 1.5rem;
  }

  /* Experience Mobile */
  .timeline-connector {
    left: 20px;
  }

  .timeline-container::before {
    left: 20px;
  }

  .timeline-dot {
    width: 40px;
    height: 40px;
  }

  .timeline-content {
    padding-left: 60px !important;
  }

  .experience-card-roadmap {
    padding: 1.5rem;
  }

  .experience-meta-roadmap {
    flex-direction: column;
    gap: 0.5rem;
  }

  .tech-tags-roadmap {
    gap: 0.5rem;
  }

  .tech-tag-roadmap {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  /* Projects Timeline Mobile */
  .timeline-line {
    left: 30px;
    transform: none;
  }

  .timeline-project {
    flex-direction: column;
    align-items: flex-start;
    padding-left: 80px !important;
    padding-right: 0 !important;
    justify-content: flex-start !important;
  }

  .timeline-node-container {
    left: 30px;
    transform: none;
  }

  .timeline-node {
    width: 50px;
    height: 50px;
    font-size: 1rem;
  }

  .project-card-timeline {
    max-width: 100%;
    padding: 1.5rem;
  }

  .project-header-timeline {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .project-actions-timeline {
    flex-direction: column;
    gap: 0.8rem;
  }

  .action-btn {
    justify-content: center;
    padding: 0.8rem 1rem;
  }

  .modal-content-timeline {
    padding: 2rem;
  }

  .modal-actions-timeline {
    flex-direction: column;
  }

  .modal-btn-timeline {
    justify-content: center;
  }
}
