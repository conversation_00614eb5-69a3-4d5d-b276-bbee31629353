import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Github, Linkedin, Mail, ExternalLink, Code, Database, Globe, Smartphone, Download, Send, MapPin, Phone, Award } from 'lucide-react'
import VideoBackground from './components/VideoBackground'
import './App.css'

// Import data
import profileData from './data/profile.json'
import experienceData from './data/experience.json'
import projectsData from './data/projects.json'
import skillsData from './data/skills.json'
import certificationsData from './data/certifications.json'

function App() {
  const [activeSection, setActiveSection] = useState('home')
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [activeSkillCategory, setActiveSkillCategory] = useState('languages')

  useEffect(() => {
    const handleScroll = () => {
      const sections = ['home', 'about', 'experience', 'projects', 'technologies', 'skills', 'certifications', 'contact']
      const scrollPosition = window.scrollY + 100

      for (const section of sections) {
        const element = document.getElementById(section)
        if (element) {
          const offsetTop = element.offsetTop
          const offsetHeight = element.offsetHeight

          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(section)
            break
          }
        }
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
    setIsMenuOpen(false)
  }

  return (
    <div className="app">
      {/* Video Background */}
      <VideoBackground />

      {/* Navigation */}
      <nav className="navbar">
        <div className="nav-container">
          <motion.div
            className="nav-logo"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Code size={24} />
            <span>Hezron</span>
          </motion.div>

          <div className="nav-links">
            {['home', 'about', 'experience', 'projects', 'technologies', 'skills', 'certifications', 'contact'].map((section, index) => (
              <motion.a
                key={section}
                href={`#${section}`}
                className={activeSection === section ? 'active' : ''}
                onClick={(e) => {
                  e.preventDefault()
                  scrollToSection(section)
                }}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                {section.charAt(0).toUpperCase() + section.slice(1)}
              </motion.a>
            ))}
          </div>

          <motion.a
            href="/resume.pdf"
            className="nav-cta"
            target="_blank"
            rel="noopener noreferrer"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.8 }}
          >
            <Download size={16} />
            Resume
          </motion.a>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="home" className="hero">
        {/* Animated Corner Decorations */}
        <div className="hero-decorations">
          <motion.div
            className="corner-decoration top-left"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.5 }}
          >
            <div className="corner-lines">
              <div className="line horizontal"></div>
              <div className="line vertical"></div>
            </div>
            <div className="corner-dots">
              <span className="dot"></span>
              <span className="dot"></span>
              <span className="dot"></span>
            </div>
          </motion.div>

          <motion.div
            className="corner-decoration top-right"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.7 }}
          >
            <div className="corner-lines">
              <div className="line horizontal"></div>
              <div className="line vertical"></div>
            </div>
            <div className="corner-dots">
              <span className="dot"></span>
              <span className="dot"></span>
              <span className="dot"></span>
            </div>
          </motion.div>

          <motion.div
            className="corner-decoration bottom-left"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.9 }}
          >
            <div className="corner-lines">
              <div className="line horizontal"></div>
              <div className="line vertical"></div>
            </div>
            <div className="corner-dots">
              <span className="dot"></span>
              <span className="dot"></span>
              <span className="dot"></span>
            </div>
          </motion.div>

          <motion.div
            className="corner-decoration bottom-right"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 1.1 }}
          >
            <div className="corner-lines">
              <div className="line horizontal"></div>
              <div className="line vertical"></div>
            </div>
            <div className="corner-dots">
              <span className="dot"></span>
              <span className="dot"></span>
              <span className="dot"></span>
            </div>
          </motion.div>
        </div>

        <div className="hero-container">
          <motion.div
            className="hero-content"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <motion.p
              className="welcome-text"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              Hello, I'm
            </motion.p>

            <motion.h1
              className="hero-title"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              {profileData.name}
            </motion.h1>

            <motion.p
              className="hero-subtitle"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
            >
              {profileData.role}
            </motion.p>

            <motion.p
              className="hero-tagline"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.9 }}
            >
              {profileData.tagline}
            </motion.p>

            <motion.div
              className="hero-location"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
            >
              <MapPin size={16} />
              <span>{profileData.location}</span>
            </motion.div>

            <motion.div
              className="hero-cta"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
            >
              <button
                className="cta-primary"
                onClick={() => scrollToSection('contact')}
              >
                <Send size={16} />
                Get In Touch
              </button>
              <button
                className="cta-secondary"
                onClick={() => scrollToSection('projects')}
              >
                View My Work
              </button>
            </motion.div>

            <motion.div
              className="social-links"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.4 }}
            >
              <a href={profileData.github} className="social-link" target="_blank" rel="noopener noreferrer">
                <Github size={20} />
              </a>
              <a href={profileData.linkedin} className="social-link" target="_blank" rel="noopener noreferrer">
                <Linkedin size={20} />
              </a>
              <a href={`mailto:${profileData.email}`} className="social-link">
                <Mail size={20} />
              </a>
              <a href={profileData.website} className="social-link" target="_blank" rel="noopener noreferrer">
                <Globe size={20} />
              </a>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="about">
        <div className="container">
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2>About Me</h2>
            <p>Get to know me better</p>
          </motion.div>

          <div className="about-content">
            <motion.div
              className="about-text"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <p>{profileData.summary}</p>

              <div className="about-stats">
                <div className="stat">
                  <span className="stat-number">15+</span>
                  <span className="stat-label">Projects Completed</span>
                </div>
                <div className="stat">
                  <span className="stat-number">10K+</span>
                  <span className="stat-label">Users Served</span>
                </div>
                <div className="stat">
                  <span className="stat-number">$50K+</span>
                  <span className="stat-label">Monthly Transactions</span>
                </div>
                <div className="stat">
                  <span className="stat-number">99.9%</span>
                  <span className="stat-label">Uptime</span>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="about-specialties"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <h3>What I Do</h3>
              <div className="specialties-grid">
                <div className="specialty-item">
                  <div className="specialty-icon">
                    <Code size={24} />
                  </div>
                  <h4>Full Stack Development</h4>
                  <p>Building end-to-end web applications with modern frameworks</p>
                </div>

                <div className="specialty-item">
                  <div className="specialty-icon">
                    <Database size={24} />
                  </div>
                  <h4>AI/ML Engineering</h4>
                  <p>Developing intelligent systems with TensorFlow and PyTorch</p>
                </div>

                <div className="specialty-item">
                  <div className="specialty-icon">
                    <Globe size={24} />
                  </div>
                  <h4>API Development</h4>
                  <p>Creating robust and scalable backend services</p>
                </div>

                <div className="specialty-item">
                  <div className="specialty-icon">
                    <Smartphone size={24} />
                  </div>
                  <h4>Research & Innovation</h4>
                  <p>Published research in computer vision and medical AI</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Experience Section */}
      <section id="experience" className="experience">
        <div className="container">
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2>Experience</h2>
            <p>My professional journey</p>
          </motion.div>

          <div className="experience-timeline">
            {experienceData.map((exp, index) => (
              <motion.div
                key={exp.id}
                className="experience-item"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
              >
                <div className="experience-content">
                  <div className="experience-header">
                    <h3>{exp.title}</h3>
                    <div className="experience-meta">
                      <span className="company">{exp.company}</span>
                      <span className="period">{exp.period}</span>
                      <span className="location">{exp.location}</span>
                    </div>
                  </div>

                  <p className="experience-description">{exp.description}</p>

                  <ul className="experience-achievements">
                    {exp.achievements.map((achievement, i) => (
                      <li key={i}>{achievement}</li>
                    ))}
                  </ul>

                  <div className="experience-tech">
                    {exp.technologies.map((tech, i) => (
                      <span key={i} className="tech-tag">{tech}</span>
                    ))}
                  </div>
                </div>

                <div className="experience-timeline-dot"></div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Code Showcase */}
      <section className="code-showcase">
        <div className="container">
          <motion.div
            className="code-window"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="code-header">
              <div className="code-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <span className="code-title">portfolio.js</span>
            </div>
            <div className="code-content">
              <pre>
{`const developer = {
  name: "Vansh",
  skills: ["React", "Node.js", "Python"],
  passion: "Building amazing experiences",
  currentFocus: "Full-stack development",

  getProjects: () => {
    return this.skills.map(skill =>
      createAwesomeProject(skill)
    );
  }
};`}
              </pre>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="projects">
        <div className="container">
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2>Featured Projects</h2>
            <p>Some of my recent work</p>
          </motion.div>

          <div className="projects-grid">
            {projectsData.map((project, index) => (
              <motion.div
                key={project.id}
                className="project-card"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -10 }}
              >
                <div className="project-image">
                  <img src={project.image} alt={project.title} />
                  <div className="project-overlay">
                    <span className={`project-status ${project.status}`}>
                      {project.status === 'live' ? 'Live' : 'Completed'}
                    </span>
                  </div>
                </div>

                <div className="project-content">
                  <div className="project-header">
                    <h3>{project.title}</h3>
                    <span className="project-category">{project.category}</span>
                  </div>

                  <p>{project.description}</p>

                  <div className="project-features">
                    {project.features.slice(0, 3).map((feature, i) => (
                      <span key={i} className="feature-item">• {feature}</span>
                    ))}
                  </div>

                  <div className="project-tech">
                    {project.technologies.map((tech, i) => (
                      <span key={i} className="tech-tag">{tech}</span>
                    ))}
                  </div>

                  <div className="project-links">
                    {project.demo && (
                      <a href={project.demo} className="project-link primary" target="_blank" rel="noopener noreferrer">
                        <ExternalLink size={16} />
                        Live Demo
                      </a>
                    )}
                    {project.github && (
                      <a href={project.github} className="project-link" target="_blank" rel="noopener noreferrer">
                        <Github size={16} />
                        Code
                      </a>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Technologies Section */}
      <section id="technologies" className="technologies">
        <div className="container">
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2>Technologies I Work With</h2>
            <p>Building modern solutions with cutting-edge technologies</p>
          </motion.div>

          <div className="technologies-slider">
            <div className="technologies-track">
              {/* First set of technologies */}
              {skillsData.technologies.map((tech, index) => (
                <div
                  key={`tech-1-${index}`}
                  className="tech-item"
                >
                  <div className="tech-icon">
                    <img src={tech.icon} alt={tech.name} />
                  </div>
                  <span className="tech-name">{tech.name}</span>
                </div>
              ))}

              {/* Duplicate set for seamless loop */}
              {skillsData.technologies.map((tech, index) => (
                <div
                  key={`tech-2-${index}`}
                  className="tech-item"
                >
                  <div className="tech-icon">
                    <img src={tech.icon} alt={tech.name} />
                  </div>
                  <span className="tech-name">{tech.name}</span>
                </div>
              ))}
            </div>
          </div>

          <motion.div
            className="tech-note"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <p>And many more tools in my ever-expanding toolkit</p>
          </motion.div>
        </div>
      </section>

      {/* Skills Section */}
      <section id="skills" className="skills">
        <div className="container">
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2>Skills & Expertise</h2>
            <p>Detailed breakdown of my technical proficiency</p>
          </motion.div>

          <div className="skills-container">
            {/* Skills Navigation */}
            <motion.div
              className="skills-nav"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
            >
              {Object.keys(skillsData).filter(key => key !== 'technologies').map((category, index) => (
                <motion.button
                  key={category}
                  className={`skill-nav-btn ${activeSkillCategory === category ? 'active' : ''}`}
                  onClick={() => setActiveSkillCategory(category)}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span className="nav-icon">
                    {category === 'languages' && '💻'}
                    {category === 'frameworks' && '⚡'}
                    {category === 'mlTools' && '🤖'}
                    {category === 'tools' && '🛠️'}
                  </span>
                  <span className="nav-text">
                    {category === 'languages' && 'Languages'}
                    {category === 'frameworks' && 'Frameworks'}
                    {category === 'mlTools' && 'AI/ML Tools'}
                    {category === 'tools' && 'Dev Tools'}
                  </span>
                </motion.button>
              ))}
            </motion.div>

            {/* Skills Content */}
            <motion.div
              className="skills-content"
              key={activeSkillCategory}
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="skills-grid-advanced">
                {skillsData[activeSkillCategory]?.map((skill, index) => (
                  <motion.div
                    key={skill.name}
                    className="skill-card-advanced"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    whileHover={{ y: -8, scale: 1.02 }}
                  >
                    <div className="skill-header">
                      <div className="skill-icon-wrapper">
                        <span className="skill-icon">{skill.icon}</span>
                      </div>
                      <div className="skill-info">
                        <h4 className="skill-name">{skill.name}</h4>
                        <span className="skill-percentage">{skill.level}%</span>
                      </div>
                    </div>

                    <div className="skill-progress-container">
                      <div className="skill-bar-advanced">
                        <motion.div
                          className="skill-progress-advanced"
                          initial={{ width: 0 }}
                          animate={{ width: `${skill.level}%` }}
                          transition={{ duration: 1.5, delay: index * 0.1, ease: "easeOut" }}
                        />
                      </div>
                    </div>

                    <div className="skill-level-text">
                      {skill.level >= 90 && <span className="level-badge expert">Expert</span>}
                      {skill.level >= 80 && skill.level < 90 && <span className="level-badge advanced">Advanced</span>}
                      {skill.level >= 70 && skill.level < 80 && <span className="level-badge intermediate">Intermediate</span>}
                      {skill.level < 70 && <span className="level-badge beginner">Beginner</span>}
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Certifications Section */}
      <section id="certifications" className="certifications">
        <div className="container">
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2>Certifications</h2>
            <p>Professional achievements and credentials</p>
          </motion.div>

          <div className="certifications-grid">
            {certificationsData.map((cert, index) => (
              <motion.div
                key={cert.id}
                className="certification-card"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -5 }}
              >
                <div className="certification-header">
                  <div className="certification-icon">
                    <Award size={24} />
                  </div>
                  <div className="certification-meta">
                    <h3>{cert.title}</h3>
                    <p className="certification-issuer">{cert.issuer}</p>
                    <p className="certification-date">{cert.date}</p>
                  </div>
                </div>

                <p className="certification-description">{cert.description}</p>

                <div className="certification-skills">
                  {cert.skills.map((skill, i) => (
                    <span key={i} className="skill-tag">{skill}</span>
                  ))}
                </div>

                <a
                  href={cert.verificationUrl}
                  className="certification-link"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <ExternalLink size={16} />
                  Verify Certificate
                </a>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="contact">
        <div className="container">
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2>Get In Touch</h2>
            <p>Let's work together on your next project</p>
          </motion.div>

          <div className="contact-content">
            <motion.div
              className="contact-info"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <h3>Let's Connect</h3>
              <p>
                I'm always interested in new opportunities and exciting projects.
                Whether you have a question or just want to say hi, feel free to reach out!
              </p>

              <div className="contact-details">
                <div className="contact-item">
                  <Mail size={20} />
                  <div>
                    <span className="contact-label">Email</span>
                    <a href={`mailto:${profileData.email}`} className="contact-value">
                      {profileData.email}
                    </a>
                  </div>
                </div>

                <div className="contact-item">
                  <Phone size={20} />
                  <div>
                    <span className="contact-label">Phone</span>
                    <a href={`tel:${profileData.phone}`} className="contact-value">
                      {profileData.phone}
                    </a>
                  </div>
                </div>

                <div className="contact-item">
                  <MapPin size={20} />
                  <div>
                    <span className="contact-label">Location</span>
                    <span className="contact-value">{profileData.location}</span>
                  </div>
                </div>
              </div>

              <div className="contact-social">
                <a href={profileData.github} className="social-link" target="_blank" rel="noopener noreferrer">
                  <Github size={20} />
                </a>
                <a href={profileData.linkedin} className="social-link" target="_blank" rel="noopener noreferrer">
                  <Linkedin size={20} />
                </a>
                <a href={profileData.website} className="social-link" target="_blank" rel="noopener noreferrer">
                  <Globe size={20} />
                </a>
              </div>
            </motion.div>

            <motion.div
              className="contact-form-container"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <form className="contact-form">
                <div className="form-group">
                  <label htmlFor="name">Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    placeholder="Your Name"
                    className="form-input"
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="email">Email</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    placeholder="<EMAIL>"
                    className="form-input"
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="subject">Subject</label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    placeholder="Project Inquiry"
                    className="form-input"
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="message">Message</label>
                  <textarea
                    id="message"
                    name="message"
                    placeholder="Tell me about your project..."
                    className="form-textarea"
                    rows="5"
                    required
                  ></textarea>
                </div>

                <button type="submit" className="form-submit">
                  <Send size={16} />
                  Send Message
                </button>
              </form>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <p>&copy; 2024 {profileData.name}. All rights reserved.</p>
            <p>Built with React, Framer Motion, and lots of ☕</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
