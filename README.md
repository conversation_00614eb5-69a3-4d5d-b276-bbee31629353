# <PERSON><PERSON><PERSON> - Software Engineer <PERSON><PERSON>

A modern, professional portfolio website showcasing my work as an AI/ML Engineer and Full Stack Developer. Built with cutting-edge technologies and featuring smooth animations, responsive design, and a clean, professional aesthetic.

## 🚀 Live Demo

Visit the live portfolio: [https://hezron-portfolio.vercel.app](https://hezron-portfolio.vercel.app)

## ✨ Features

### 🎨 Modern Design
- **Clean, Professional Layout** - Minimalist design with focus on content
- **Dark Theme** - Elegant dark color scheme with gradient accents
- **Responsive Design** - Mobile-first approach, works on all devices
- **Smooth Animations** - Powered by Framer Motion for engaging user experience

### 📱 Sections
- **Hero Section** - Introduction with animated corner decorations and CTAs
- **About Me** - Professional summary with statistics and specialties
- **Experience** - Timeline view of work history with achievements
- **Projects** - Showcase of featured projects with live demos and code links
- **Skills** - Categorized skills with proficiency levels and progress bars
- **Certifications** - Professional credentials and achievements
- **Contact** - Contact form and social links

### 🛠️ Technical Features
- **JSON-Driven Content** - Easy to update content through data files
- **Smooth Scrolling** - Enhanced navigation experience
- **Performance Optimized** - Fast loading and smooth interactions
- **SEO Ready** - Optimized for search engines
- **Vercel Ready** - One-click deployment

## 🛠️ Tech Stack

### Frontend
- **React 18** - Modern React with hooks and functional components
- **Vite** - Fast build tool and development server
- **Framer Motion** - Smooth animations and transitions
- **Lucide React** - Beautiful, customizable icons

### Styling
- **CSS3** - Custom CSS with modern features
- **CSS Grid & Flexbox** - Responsive layout system
- **CSS Variables** - Consistent theming
- **Gradient Effects** - Modern visual elements

### Development
- **ESLint** - Code quality and consistency
- **Hot Reload** - Fast development experience
- **Modern JavaScript** - ES6+ features

## �� Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/huckbyte/hezron-portfolio.git
   cd hezron-portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

### Build for Production

```bash
npm run build
```

### Preview Production Build

```bash
npm run preview
```

## 📝 Customization

### Updating Content

All content is stored in JSON files in the `src/data/` directory:

- **profile.json** - Personal information, contact details, and summary
- **experience.json** - Work history and achievements
- **projects.json** - Featured projects with descriptions and links
- **skills.json** - Technical skills organized by category
- **certifications.json** - Professional certifications and credentials
- **education.json** - Educational background

## 🚀 Deployment

### Vercel (Recommended)

1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Deploy**
   ```bash
   npm run build
   vercel
   ```

3. **Follow the prompts** to configure your deployment

## 👨‍💻 Author

**Hezron Paipai**
- Website: [https://venomx.vercel.app](https://venomx.vercel.app)
- GitHub: [@huckbyte](https://github.com/huckbyte)
- Email: <EMAIL>
- LinkedIn: [Hezron Paipai](https://linkedin.com/in/hezron-paipai)

---

⭐ **Star this repository if you found it helpful!**
